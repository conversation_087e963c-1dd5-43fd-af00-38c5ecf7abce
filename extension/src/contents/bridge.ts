import type { PlasmoCSConfig } from 'plasmo';
import { RuntimeActionResponse } from '~/types';

export const config: PlasmoCSConfig = {
  matches: ['<all_urls>'], // Or restrict to specific domains
  run_at: 'document_idle',
};

chrome.runtime.sendMessage({ name: 'load-scripts' }, (response: RuntimeActionResponse) => {
  (response.data as string[]).forEach((script: string) => {
    const blob = new Blob([script], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);

    const s = document.createElement('script');
    s.type = 'module';
    s.src = url;
    s.onload = () => URL.revokeObjectURL(url); // cleanup
    (document.head || document.documentElement).appendChild(s);
  });
});
