import { db } from '~/storages/indexdb';
import { MiniApp, Developing, Installation } from '@the-agent/shared';
import { deleteConversation } from './conversation';
import { createApiClient } from './api/client';

export const getMiniapp = async (id: number): Promise<MiniApp> => {
  const miniapp = await db.getMiniapp(id);
  if (!miniapp) {
    throw new Error('MiniApp not found');
  }
  return miniapp;
};

export const deleteMiniapp = async (id: number): Promise<void> => {
  // Find miniapp first to locate its conversation
  const miniapp = await db.getMiniapp(id);
  if (!miniapp) return;

  // Delete associated conversation and messages using the conversationId
  if (miniapp.conversation_id != null) {
    await deleteConversation(miniapp.conversation_id);
  }

  // Remove the miniapp record itself
  await db.apps.delete(id);
};

export const updateMiniapp = async (
  id: number,
  update: Partial<Pick<MiniApp, 'name' | 'installation' | 'status'>>
): Promise<void> => {
  const client = await createApiClient();
  await client.updateMiniApp({ id, ...update });
  await db.updateMiniapp(id, update);
};

// Update miniapp developing field and trigger sync
export const updateMiniappDeveloping = async (
  id: number,
  developing: Developing
): Promise<void> => {
  await db.updateMiniapp(id, { developing });

  // Send sync message to web via background script
  await chrome.runtime.sendMessage({
    name: 'trigger-script-sync',
    body: {
      type: 'script-updated',
      miniappId: id,
      data: {
        code: developing.code,
        version: developing.version,
        updated_at: developing.updated_at,
      },
    },
  });
};

export const updateMiniappInstallation = async (id: number, installation: Installation) => {
  const miniapp = await db.getMiniapp(id);
  if (!miniapp) {
    throw new Error('MiniApp not found');
  }

  // Add installation to history if current installation is null/undefined or codes are different
  const shouldAddToHistory =
    !miniapp.installation ||
    JSON.stringify(miniapp.installation.code) !== JSON.stringify(installation.code);

  if (shouldAddToHistory) {
    miniapp.history.push(installation);
  }

  miniapp.installation = installation;
  await db.updateMiniapp(id, miniapp);
};
