import React from 'react';
import noMiniappImage from '~/assets/imgs/no-miniapp.png';
import { useLanguage } from '~/utils/i18n';

interface MiniappEmptyStateProps {
  fromArchived: boolean;
  onClose?: () => void;
}

const MiniappEmptyState: React.FC<MiniappEmptyStateProps> = ({ fromArchived }) => {
  const { getMessage } = useLanguage();
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        backgroundColor: '#ffffff',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      }}
    >
      {/* Main Content */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          flex: 1,
          padding: '20px 20px 40px 20px',
          gap: '24px',
        }}
      >
        {/* Illustration */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '8px',
          }}
        >
          <img
            src={noMiniappImage}
            alt="No MiniApp illustration"
            style={{
              width: '200px',
              height: 'auto',
              opacity: 0.8,
            }}
          />
        </div>

        {/* Text Content */}
        <div
          style={{
            textAlign: 'center',
            maxWidth: '300px',
          }}
        >
          <h2
            style={{
              fontSize: '20px',
              fontWeight: 600,
              color: '#111827',
              margin: '0 0 8px 0',
              lineHeight: 1.3,
            }}
          >
            {fromArchived ? getMessage('emptyArchivedMiniapps') : getMessage('emptyMiniapps')}
          </h2>
        </div>
      </div>
    </div>
  );
};

export default MiniappEmptyState;
