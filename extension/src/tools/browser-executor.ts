import { Tool<PERSON><PERSON>, Too<PERSON><PERSON>xecutor, ToolCallResult, Message } from '@the-agent/shared';
import OpenAI from 'openai';
import { GlobalContext } from '~/types/task';
import {
  executeTabToolkit,
  executeWebToolkit,
  parseToolParams,
  toOpenAITools,
} from '~/utils/toolkit';
import { TaskToolExecutor } from './task-executor';
import { ToolDescription, WebContextInput } from '~/types';
import { TaskNode } from '@the-agent/shared/src/types/task';
import {
  TAB_TOOLKIT_TOOLS,
  WEB_TOOLKIT_ACTION_TOOLS,
  WEB_TOOLKIT_ANALYZE_TOOLS,
  SITE_MEMORY_TOOLS,
  TASK_TOOLKIT_TOOLS,
} from './tool-descriptions';
import { createApiClient } from '~/services/api/client';

export class BrowserToolExecutor implements ToolExecutor {
  async execute(toolCall: ToolCall): Promise<ToolCallResult> {
    try {
      if (!toolCall.function.name) {
        throw new Error('Tool name is required');
      }

      const toolName = toolCall.function.name;
      const params = parseToolParams(toolCall);
      const { context, ...rest } = params as { context: WebContextInput; [key: string]: unknown };
      return await this.executeInternal(toolName, rest);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.error('Tool execution failed:', message);
      return { success: false, error: message };
    }
  }

  getTools(): OpenAI.ChatCompletionTool[] {
    const tools = toOpenAITools([
      ...TAB_TOOLKIT_TOOLS,
      ...WEB_TOOLKIT_ACTION_TOOLS,
      ...WEB_TOOLKIT_ANALYZE_TOOLS,
      ...SITE_MEMORY_TOOLS,
    ]);
    return tools;
  }

  getPostToolcallMessage(toolCall: ToolCall): string {
    if (!toolCall.result) {
      throw new Error('Tool call result is required');
    }
    return '';
  }

  async executeInternal(toolName: string, params: unknown): Promise<ToolCallResult> {
    if (toolName.startsWith('TabToolkit_')) {
      return await executeTabToolkit(toolName, params);
    } else if (toolName.startsWith('WebToolkit_')) {
      return await executeWebToolkit(toolName, params);
    } else if (toolName.startsWith('SiteMemoryToolkit_')) {
      return await this.executeSiteMemoryTool(toolName, params);
    } else {
      throw new Error(`Unsupported tool call: ${toolName}`);
    }
  }

  private async executeSiteMemoryTool(toolName: string, params: unknown): Promise<ToolCallResult> {
    if (toolName === 'SiteMemoryToolkit_searchSiteMemory') {
      return await this.searchSiteMemory(
        params as { hostname: string; query: string; limit?: number }
      );
    }
    throw new Error(`Unsupported site memory tool: ${toolName}`);
  }

  private async searchSiteMemory(params: {
    hostname: string;
    query: string;
    limit?: number;
  }): Promise<ToolCallResult> {
    try {
      const apiClient = await createApiClient();
      const response = await apiClient.searchMemoryV2({
        text: params.query,
        config: {
          limit: params.limit || 5,
          filters: {
            hostname: params.hostname,
            memoryType: 'site',
          },
        },
      });

      return {
        success: true,
        data: response.results || [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

export class BrowserTaskToolExecutor extends TaskToolExecutor {
  private browserToolExecutor: BrowserToolExecutor;

  constructor(c: GlobalContext) {
    super(c);
    this.browserToolExecutor = new BrowserToolExecutor();
  }

  getToolDescriptions(): ToolDescription[] {
    const tools = [
      ...TAB_TOOLKIT_TOOLS,
      ...WEB_TOOLKIT_ACTION_TOOLS,
      ...WEB_TOOLKIT_ANALYZE_TOOLS,
      ...SITE_MEMORY_TOOLS,
      ...TASK_TOOLKIT_TOOLS,
    ];
    return tools;
  }

  setReminderMessageContent(toolCall: ToolCall, _reminderMessage: Message): void {
    throw new Error(`Invalid tool call: ${toolCall.function.name}`);
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    // Check if the tool call failed and provide helpful suggestions
    if (!toolCall.result?.success && toolCall.result?.error) {
      const error = toolCall.result.error;

      // Check if it's a selector-related error
      if (error.includes('Element with selector') && error.includes('not found')) {
        // Check if it looks like a label-based selector
        if (toolCall.function.arguments) {
          try {
            const args = JSON.parse(toolCall.function.arguments);
            const selectorOrIndex = args.selectorOrIndex;
            if (selectorOrIndex && selectorOrIndex.includes('[label=')) {
              return `⚠️ The selector "${selectorOrIndex}" appears to be a label-based selector which may be unstable due to dynamic content. Try using the element index from WebToolkit_analyzePageDOM instead for better reliability.`;
            }
          } catch (e) {
            // Ignore parsing errors
          }
        }
      }
    }

    return '';
  }

  isFinalToolCall(toolCall: ToolCall) {
    return toolCall.function.name === 'TaskToolkit_finishTask';
  }

  async executeInternal(
    _task: TaskNode,
    toolName: string,
    params: unknown
  ): Promise<ToolCallResult> {
    return this.browserToolExecutor.executeInternal(toolName, params);
  }
}
