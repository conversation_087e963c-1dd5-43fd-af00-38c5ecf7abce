import { MiniApp, UpdateMiniAppRequest, ListMiniappResponseType } from '@the-agent/shared';
import { MiniAppStore } from '../interfaces/miniapp.store';
import { Pool } from 'pg';
import { sha256Hex } from '../utils/crypto';

export class PostgresMiniAppStore implements MiniAppStore {
  private pool: Pool;
  private defaultDeveloping = {
    code: '',
    version: 1,
    updated_at: Date.now(),
  };
  private defaultInstallation = {
    code: '',
    changelogs: '',
    deployed_at: Date.now(),
  };

  constructor(pool: Pool) {
    this.pool = pool;
  }

  async saveMiniApp(miniapp: MiniApp, userId: string): Promise<void> {
    const client = await this.pool.connect();
    const convId = await sha256Hex(`${userId}:${miniapp.conversation_id}`);
    const miniappQuery = `
      INSERT INTO miniapps (
        id, user_id, local_id, conversation_id, name, developing, installation, history, status
      ) VALUES (md5(concat_ws(':', $1::bigint::text, $2::text)), $1, $2, $3, $4, $5, $6, $7, $8)
    `;

    const miniappValues = [
      userId,
      miniapp.id,
      convId,
      miniapp.name,
      miniapp.developing ?? this.defaultDeveloping,
      miniapp.installation ?? this.defaultInstallation,
      miniapp.history,
      miniapp.status,
    ];

    try {
      await client.query('BEGIN');
      await client.query(miniappQuery, miniappValues);
      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw new Error(`Failed to save miniapp: ${error}`);
    } finally {
      client.release();
    }
  }

  async updateMiniApp(userId: string, request: UpdateMiniAppRequest): Promise<void> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Load current
      const { rows } = await client.query(
        'SELECT name, developing, installation, history, status FROM miniapps WHERE user_id = $1 AND local_id = $2 LIMIT 1',
        [userId, request.id]
      );
      const current = rows[0];
      if (!current) {
        await client.query('ROLLBACK');
        return;
      }

      const fields: string[] = [];
      const values: unknown[] = [];
      let i = 1;

      if (request.name !== undefined) {
        fields.push(`name = $${i++}`);
        values.push(request.name);
      }
      if (request.developing !== undefined) {
        fields.push(`developing = $${i++}`);
        values.push(request.developing);
      }
      if (request.status !== undefined) {
        fields.push(`status = $${i++}`);
        values.push(request.status);
      }

      if (request.installation !== undefined) {
        const sameInstallation =
          JSON.stringify(current.installation?.code ?? null) ===
          JSON.stringify(request.installation?.code ?? null);
        if (!sameInstallation) {
          const newHistory = Array.isArray(current.history) ? [...current.history] : [];
          if (current.installation != null) {
            newHistory.push(current.installation);
          }
          fields.push(`installation = $${i++}`);
          values.push(request.installation ?? this.defaultInstallation);
          fields.push(`history = $${i++}`);
          values.push(newHistory);
        }
      }

      if (fields.length === 0) {
        await client.query('ROLLBACK');
        return;
      }

      const userIdIndex = i++;
      const localIdIndex = i++;
      const query = `UPDATE miniapps SET ${fields.join(', ')} WHERE user_id = $${userIdIndex} AND local_id = $${localIdIndex}`;
      values.push(userId, request.id);

      await client.query(query, values);
      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw new Error(`Failed to update miniapp: ${error}`);
    } finally {
      client.release();
    }
  }

  async listMiniApps(userId: string, startFrom: number): Promise<ListMiniappResponseType[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT
          m.local_id as miniapp_id,
          m.name,
          m.developing,
          m.installation,
          m.history,
          m.status,
          c.local_id as conversation_id,
          c.type as conversation_type,
          c.last_selected_at as conversation_last_selected_at,
          COALESCE(
            json_agg(
              CASE WHEN msg.local_id IS NOT NULL THEN
                json_build_object(
                  'local_id', msg.local_id,
                  'conversation_id', c.local_id,
                  'role', msg.role,
                  'content', msg.content,
                  'reasoning', msg.reasoning,
                  'tool_calls', msg.tool_calls,
                  'tool_call_id', msg.tool_call_id,
                  'name', msg.name,
                  'status', msg.status,
                  'error', msg.error,
                  'actor', msg.actor,
                  'task_id', msg.task_id,
                  'run_id', msg.run_id,
                  'agent_id', msg.agent_id,
                  'metadata', msg.metadata
                )
              END
            ) FILTER (WHERE msg.local_id IS NOT NULL),
            '[]'::json
          ) as messages
        FROM miniapps m
        INNER JOIN conversations c ON c.id = m.conversation_id
        LEFT JOIN messages msg ON c.id = msg.conversation_id
        WHERE m.user_id = $1 AND c.user_id = $1 AND c.status = 'active' AND m.local_id >= $2
        GROUP BY m.local_id, m.name, m.developing, m.installation, m.history, m.status,
                 c.local_id, c.type, c.last_selected_at
        ORDER BY c.last_selected_at DESC NULLS LAST, m.local_id DESC
      `;

      const { rows } = await client.query(query, [userId, startFrom]);

      return rows.map((row: any) => ({
        id: row.miniapp_id,
        conversation_id: row.conversation_id,
        name: row.name,
        developing: row.developing ? JSON.parse(row.developing) : {},
        installation: row.installation ? JSON.parse(row.installation) : {},
        history: Array.isArray(row.history) ? row.history : [],
        status: row.status ?? 'active',
        conversation: {
          id: row.conversation_id,
          type: row.conversation_type,
          last_selected_at: row.conversation_last_selected_at || row.conversation_id,
          messages: row.messages.map((message: any) => ({
            id: message.local_id,
            conversation_id: row.conversation_id,
            role: message.role,
            content: message.content,
            reasoning: message.reasoning,
            tool_calls: this.parseToolCalls(message.tool_calls),
            tool_call_id: message.tool_call_id,
            name: message.name,
            status: message.status,
            error: message.error,
            actor: message.actor,
            task_id: message.task_id,
            run_id: message.run_id,
            agent_id: message.agent_id,
            metadata: message.metadata ? JSON.parse(message.metadata) : {},
          })),
        },
      }));
    } finally {
      client.release();
    }
  }

  private parseToolCalls(toolCalls: unknown): any[] | null {
    if (typeof toolCalls === 'string') {
      return JSON.parse(toolCalls);
    }
    if (toolCalls === null) {
      return null;
    }
    return toolCalls as any[];
  }
}
